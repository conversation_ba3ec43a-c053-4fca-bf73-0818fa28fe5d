// adaptive-login-form.tsx
import { useState, useEffect } from "react";
import { useRouter, useSearch } from "@tanstack/react-router";
import { motion, AnimatePresence } from "framer-motion";
import { GoogleLoginButton } from "@/components/auth/google-login-button";
import { GithubLoginButton } from "@/components/auth/github-login-button";
import { ArrowLeft, ChevronDown, ChevronUp } from "lucide-react";

interface AdaptiveLoginFormProps {
  callbackUrl?: string;
}

interface OAuthProvider {
  id: 'google' | 'github' | 'apple';
  name: string;
  component: React.ReactNode;
  available: boolean;
}

export default function AdaptiveLoginForm({ callbackUrl = "/account" }: AdaptiveLoginFormProps) {
  const [errorMessage, setErrorMessage] = useState("");
  const [currentImageLoaded, setCurrentImageLoaded] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const router = useRouter();
  const searchParams = useSearch({ strict: false });

  // Extract parameters from URL
  const from = (searchParams as any)?.from;
  const authProvider = (searchParams as any)?.auth_provider;
  const isFromMobile = from === 'mobile';

  // Handler for auth success
  const handleAuthSuccess = () => {
    router.navigate({ to: callbackUrl });
  };

  // Handler for auth errors
  const handleAuthError = (error: Error) => {
    setErrorMessage(`Authentication failed: ${error.message || "Unknown error"}`);
    setIsDropdownOpen(false);
  };

  // Handler to go back to mobile app
  const handleBackToMobile = () => {
    const mobileUrl = "mobile://back";
    window.location.href = mobileUrl;
    setTimeout(() => {
      setErrorMessage("Please return to the mobile app to continue.");
    }, 1000);
  };

  // Define available OAuth providers
  const availableProviders: OAuthProvider[] = [
    {
      id: 'google',
      name: 'Google',
      component: <GoogleLoginButton onSuccess={handleAuthSuccess} onError={handleAuthError} />,
      available: !isFromMobile || authProvider === 'google' || authProvider === 'all'
    },
    {
      id: 'github',
      name: 'GitHub',
      component: <GithubLoginButton onSuccess={handleAuthSuccess} onError={handleAuthError} />,
      available: !isFromMobile // Only show GitHub for direct web access
    },
    {
      id: 'apple',
      name: 'Apple',
      component: (
        <div className="text-center">
          <p className="text-sm text-[#7e7b76] mb-1 font-manrope_1">Apple Sign In</p>
          <p className="text-xs text-[#7e7b76] opacity-70 font-manrope_1">
            Only available on iOS devices
          </p>
        </div>
      ),
      available: isFromMobile && (authProvider === 'apple' || authProvider === 'all')
    }
  ].filter(provider => provider.available);

  // Determine primary and secondary providers
  const getPrimaryProvider = (): OAuthProvider => {
    if (isFromMobile && authProvider) {
      const preferredProvider = availableProviders.find(p => p.id === authProvider);
      if (preferredProvider) return preferredProvider;
    }
    // Default to Google if available, otherwise first available
    return availableProviders.find(p => p.id === 'google') || availableProviders[0];
  };

  const primaryProvider = getPrimaryProvider();
  const secondaryProviders = availableProviders.filter(p => p.id !== primaryProvider?.id);

  // Track when the image is loaded
  useEffect(() => {
    setCurrentImageLoaded(false);
    const img = new Image();
    img.src = "/MiloSignin.png";
    img.onload = () => setCurrentImageLoaded(true);
    img.onerror = () => {
      console.warn("Failed to load MiloSignin.png image");
      setCurrentImageLoaded(true);
    };
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const dropdown = document.getElementById('oauth-dropdown');
      if (dropdown && !dropdown.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isDropdownOpen]);

  return (
    <div className="w-full overflow-hidden rounded-xl">
      {/* Mock window controls with caption */}
      <div className="flex items-center gap-4 px-6 md:px-8 py-3 bg-[#f5f2ea] dark:bg-[#0f0c05] rounded-t-xl">
        <div className="text-[#7e7b76] text-xs text-left md:text-sm font-manrope_1 italic">
          {isFromMobile
            ? "Continue your journey from the mobile app..."
            : "Secure access for existing members only..."
          }
        </div>
      </div>

      {/* Content area */}
      <div>
        {/* Image section */}
        <div className="bg-[#e9e5dc] dark:bg-[#1e1b16] onboarding-image-container overflow-hidden">
          <div className="w-full flex items-center justify-center relative">
            <div
              className={`absolute inset-0 bg-[#1e1b16] z-10 transition-opacity duration-300 ${
                currentImageLoaded ? "opacity-0" : "opacity-100"
              }`}
            />
            <img
              src="/MiloSignin.png"
              alt="Milo Sign In"
              className="w-full h-auto max-h-[300px] sm:max-h-[350px] md:max-h-[400px] lg:max-h-[calc(100vh-356px)] opacity-80 onboarding-image relative z-[2]"
              style={{ opacity: currentImageLoaded ? 0.8 : 0 }}
              onLoad={() => setCurrentImageLoaded(true)}
            />
          </div>
        </div>

        {/* Text and OAuth section */}
        <div className="bg-[#f5f2ea] dark:bg-[#0f0c05] rounded-b-xl">
          {/* Text content */}
          <div className="flex flex-col font-manrope_1 items-start justify-center px-4 pt-8 mx-auto w-full max-w-[calc(100%-1rem)] md:max-w-[calc(100%-2rem)]">
            {/* Error message */}
            {errorMessage && (
              <div className="w-full mb-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                <p className="text-red-700 dark:text-red-300 text-sm font-manrope_1">{errorMessage}</p>
              </div>
            )}

            {/* Context-specific messaging */}
            {isFromMobile ? (
              <>
                <h1 className="text-lg font-semibold text-[#7e7b76] mb-2">
                  Continue from Mobile
                </h1>
                {authProvider === 'apple' && (
                  <p className="text-[#7e7b76] text-left text-sm mb-4">
                    Sign in with the same Apple account you used in the mobile app
                  </p>
                )}
                {authProvider === 'google' && (
                  <p className="text-[#7e7b76] text-left text-sm mb-4">
                    Sign in with the same Google account you used in the mobile app
                  </p>
                )}
                {authProvider === 'all' && (
                  <p className="text-[#7e7b76] text-left text-sm mb-4">
                    Sign in with any method since you have multiple authentication options set up
                  </p>
                )}
              </>
            ) : (
              <>
 <h1 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-black dark:from-white via-gray-700 dark:via-gray-300 to-gray-600 dark:to-gray-400 py-2 font-manrope_1">
              <span className="italic">Sign in</span> to continue
            </h1>
                <p className="text-[#7e7b76] font-manrope_1 text-left text-sm mb-2">
                  Choose your preferred sign-in method to access the admin dashboard
                </p>
              </>
            )}
          </div>

          {/* Footer with OAuth buttons */}
          <div className="border-t border-[#d6d1c4] dark:border-[#29261f] mt-8 flex items-stretch justify-between relative min-h-[64px] md:min-h-[72px] rounded-b-xl overflow-visible">

            {/* Left section - Back to mobile button (if mobile flow) or Admin Access */}
            <div className="border-r border-[#d6d1c4] dark:border-[#29261f] flex items-center">
              {isFromMobile ? (
                <button
                  type="button"
                  onClick={handleBackToMobile}
                  className="font-manrope_1 transition-colors py-4 px-6 md:px-8 text-[#7e7b76] hover:text-black dark:hover:text-white flex items-center h-full"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Mobile App
                </button>
              ) : (
                <div className="font-manrope_1 py-4 px-6 text-[#7e7b76] flex items-center h-full">
                  Admin Access
                </div>
              )}
            </div>

            {/* Center section - OAuth buttons with dropdown */}
            <div className="flex-1 w-full flex items-center justify-center px-4 py-4 border-r border-[#d6d1c4] dark:border-[#29261f] relative">
              <div className="flex items-center gap-2" id="oauth-dropdown">
                {/* Primary OAuth Provider */}
                <div className="max-w-[200px]">
                  {primaryProvider?.component}
                </div>

                {/* Dropdown for additional providers */}
                {secondaryProviders.length > 0 && (
                  <div className="relative">
                    <button
                      type="button"
                      onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                      className="p-2 text-[#7e7b76] hover:text-black dark:hover:text-white transition-colors rounded-md hover:bg-[#e9e5dc] dark:hover:bg-[#1e1b16]"
                      aria-label="More sign-in options"
                    >
                      <motion.div
                        animate={{ rotate: isDropdownOpen ? 180 : 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <ChevronDown className="w-4 h-4" />
                      </motion.div>
                    </button>

                    {/* Dropdown Menu */}
                    <AnimatePresence>
                      {isDropdownOpen && (
                        <motion.div
                          initial={{ opacity: 0, y: -10, scale: 0.95 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          exit={{ opacity: 0, y: -10, scale: 0.95 }}
                          transition={{ duration: 0.2 }}
                          className="absolute bottom-full mb-2 right-0 bg-white dark:bg-[#1e1b16] border border-[#d6d1c4] dark:border-[#29261f] rounded-lg shadow-lg overflow-hidden z-50 min-w-[250px]"
                        >
                          <div className="p-2">
                            <div className="text-xs font-manrope_1 text-[#7e7b76] px-3 py-2 border-b border-[#d6d1c4] dark:border-[#29261f]">
                              More sign-in options
                            </div>
                            {secondaryProviders.map((provider, index) => (
                              <motion.div
                                key={provider.id}
                                initial={{ opacity: 0, x: -10 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.1 }}
                                className="p-3 hover:bg-[#f5f2ea] dark:hover:bg-[#0f0c05] transition-colors"
                                onClick={() => setIsDropdownOpen(false)}
                              >
                                {provider.component}
                              </motion.div>
                            ))}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                )}
              </div>
            </div>

         
          </div>
          
        </div>
           {/* Right section - Terms */}
            <div className="flex items-center">
              <div className="font-manrope_1 py-4 px-4 sm:px-6 text-xs sm:text-sm text-[#7e7b76] text-center">
                <span>
                  By signing in, you agree to our{" "}
                  <a href="/terms" className="underline underline-offset-4 hover:text-black dark:hover:text-white transition-colors">
                    Terms
                  </a>{" "}
                  and{" "}
                  <a href="/privacy" className="underline underline-offset-4 hover:text-black dark:hover:text-white transition-colors">
                    Privacy Policy
                  </a>
                </span>
              </div>
            </div>
      </div>
      
    </div>
  );
}